import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { AIChat } from '@/components/ai-assistant/ai-chat'

export default function HelpCenterPage() {
  return (
    <DashboardLayout
      pageHeader={{
        title: 'Help Center',
        description: 'Find answers to your questions and get support.'
      }}
    >
      <div className="space-y-6">
        <div className="text-center">
          <h2 className="text-2xl font-bold">How can we help you?</h2>
          <p className="text-muted-foreground mt-2">
            Browse our help articles or contact support for assistance.
          </p>
        </div>

        <div className="grid gap-6 lg:grid-cols-3">
          {/* AI Assistant - 右侧 2/3 */}
          <div className="lg:col-span-2">
            <AIChat embedded={true} height="600px" />
          </div>
          {/* Help Articles - 左侧 1/3 */}
          <div className="lg:col-span-1">
            <h3 className="text-lg font-semibold mb-2">Help Topics</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Browse common help topics or ask the AI assistant for personalized help.
            </p>
            <div className="space-y-3">
              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <h4 className="font-semibold mb-2 text-sm">Getting Started</h4>
                <p className="text-xs text-muted-foreground">
                  Learn the basics of using our platform.
                </p>
              </div>

              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <h4 className="font-semibold mb-2 text-sm">User Management</h4>
                <p className="text-xs text-muted-foreground">
                  How to manage users and permissions.
                </p>
              </div>

              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <h4 className="font-semibold mb-2 text-sm">MQTT Configuration</h4>
                <p className="text-xs text-muted-foreground">
                  Configure and manage MQTT connections.
                </p>
              </div>

              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <h4 className="font-semibold mb-2 text-sm">API Documentation</h4>
                <p className="text-xs text-muted-foreground">
                  Explore our comprehensive API documentation.
                </p>
              </div>

              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <h4 className="font-semibold mb-2 text-sm">Troubleshooting</h4>
                <p className="text-xs text-muted-foreground">
                  Common issues and solutions.
                </p>
              </div>

              <div className="p-4 border rounded-lg hover:shadow-md transition-shadow cursor-pointer">
                <h4 className="font-semibold mb-2 text-sm">Contact Support</h4>
                <p className="text-xs text-muted-foreground">
                  Get in touch with our support team.
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  )
}
