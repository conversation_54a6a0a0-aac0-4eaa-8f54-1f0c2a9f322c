import { useState, useCallback, useMemo } from 'react'
import { DashboardLayout } from '@/components/layout/dashboard-layout'
import { DataTable } from '@/components/data-table'
import { columns } from './components/users-columns'
import { UsersDialogs } from './components/users-dialogs'
import { UsersPrimaryButtons } from './components/users-primary-buttons'
import UsersProvider from './context/users-context'
import { useUsers } from './hooks/use-users'
import { GetUsersParams, UserRole, UserStatus, UserResponse } from '@/api/users'
import { User } from './data/schema'
import { statusOptions, roleOptions } from './data/data'

// Transform UserResponse to User
function transformUserResponse(userResponse: UserResponse): User {
  return {
    id: userResponse.id,
    username: userResponse.email.split('@')[0], // Use email prefix as username fallback
    email: userResponse.email,
    phoneNumber: undefined, // Not provided in UserResponse
    status: userResponse.status,
    role: userResponse.role,
    createdAt: new Date(userResponse.created_at),
    updatedAt: new Date(userResponse.updated_at),
    profile: userResponse.profile,
  }
}

export default function Users() {
  // 简化的状态管理
  const [params, setParams] = useState<GetUsersParams>({
    page: 1,
    page_size: 10,
  })

  // 筛选器状态
  const [filterValues, setFilterValues] = useState({
    search: '',
    status: 'all' as UserStatus | 'all',
    role: 'all' as UserRole | 'all',
  })

  // 构建当前筛选状态用于显示
  const currentServerFilters = useMemo(() => {
    const filters: Record<string, string[]> = {}
    if (params.status) {
      filters.status = [params.status]
    }
    if (params.role) {
      filters.role = [params.role]
    }
    return filters
  }, [params.status, params.role])

  // 获取用户数据
  const { data: usersResponse, isLoading, error, refetch } = useUsers(params)



  // 处理参数更新
  const handleParamsChange = useCallback((newParams: GetUsersParams) => {
    setParams(prev => ({ ...prev, ...newParams }))
  }, [])

  // 处理筛选器变化
  const handleFilterChange = useCallback((filters: any) => {
    setFilterValues(prev => ({ ...prev, ...filters }))

    // 构建API参数
    const apiParams: GetUsersParams = { page: 1 }

    if (filters.search?.trim()) {
      apiParams.search = filters.search.trim()
    }
    if (filters.status && filters.status !== 'all') {
      apiParams.status = filters.status
    }
    if (filters.role && filters.role !== 'all') {
      apiParams.role = filters.role
    }

    handleParamsChange(apiParams)
  }, [handleParamsChange])

  // 处理重置
  const handleReset = useCallback(() => {
    setFilterValues({ search: '', status: 'all', role: 'all' })
    setParams(prev => ({ 
      page: 1, 
      page_size: prev.page_size || 10 // 保留当前的page_size值
    }))
  }, [])

  // 筛选器配置
  const filterConfigs = useMemo(() => [
    {
      key: 'status',
      label: 'Status',
      type: 'select' as const,
      options: statusOptions,
      allLabel: 'All Status',
    },
    {
      key: 'role',
      label: 'Role',
      type: 'select' as const,
      options: roleOptions,
      allLabel: 'All Roles',
    },
  ], [])

  // 分页信息
  const paginationInfo = useMemo(() => {
    if (!usersResponse) return undefined
    // 使用当前参数中的page_size而不是服务器返回的page_size，确保UI立即反映用户选择
    const currentPageSize = params.page_size || 10
    return {
      page: usersResponse.page,
      pageSize: currentPageSize,
      total: usersResponse.total,
    }
  }, [usersResponse, params.page_size])

  // 转换用户数据
  const transformedUsers = useMemo(() => {
    if (!usersResponse?.items) return []
    return usersResponse.items.map(transformUserResponse)
  }, [usersResponse?.items])

  return (
    <UsersProvider>
      <DashboardLayout
        headerFixed={true}
        pageHeader={{
          title: 'User List',
          description: 'Manage your users and their roles here.',
          actions: <UsersPrimaryButtons />
        }}
      >
        <div className='-mx-4 flex-1 overflow-auto px-4 py-1 lg:flex-row lg:space-y-0 lg:space-x-12'>
          <DataTable
            columns={columns}
            data={transformedUsers}
            isLoading={isLoading}
            emptyMessage="No users found."
            searchPlaceholder="Search users..."
            searchFields={['email', 'profile.first_name', 'profile.last_name', 'profile.display_name']}
            filters={[
              {
                id: 'status',
                title: 'Status',
                options: [
                  { label: 'Active', value: 'active' },
                  { label: 'Inactive', value: 'inactive' },
                  { label: 'Pending', value: 'pending' },
                  { label: 'Banned', value: 'banned' },
                ]
              },
              {
                id: 'role',
                title: 'Role',
                options: [
                  { label: 'Admin', value: 'admin' },
                  { label: 'User', value: 'user' },
                ]
              }
            ]}
            pageSize={params.page_size || 10}
            showPagination={true}
            showToolbar={true}
            serverSidePagination={true}
            // 服务端分页配置
            serverPaginationInfo={paginationInfo ? {
              currentPage: paginationInfo.page,
              totalPages: Math.ceil(paginationInfo.total / paginationInfo.pageSize),
              pageSize: paginationInfo.pageSize,
              totalItems: paginationInfo.total,
            } : undefined}
            onServerPageChange={(page) => handleParamsChange({ page })}
            onServerPageSizeChange={(pageSize) => handleParamsChange({ page: 1, page_size: pageSize })}
            onServerSearchChange={(search) => handleParamsChange({ page: 1, search: search || undefined })}
            onServerFilterChange={(filterId, value) => {
              const newParams: Partial<GetUsersParams> = { page: 1 }
              if (filterId === 'status') {
                if (value && value !== 'all') {
                  newParams.status = value as UserStatus
                } else {
                  // 清除status筛选
                  setParams(prev => {
                    const { status, ...rest } = prev
                    return { ...rest, page: 1 }
                  })
                  return
                }
              }
              if (filterId === 'role') {
                if (value && value !== 'all') {
                  newParams.role = value as UserRole
                } else {
                  // 清除role筛选
                  setParams(prev => {
                    const { role, ...rest } = prev
                    return { ...rest, page: 1 }
                  })
                  return
                }
              }
              handleParamsChange(newParams)
            }}
            // 传递当前筛选状态
            serverSearchValue={params.search || ''}
            serverSelectedFilters={currentServerFilters}
          />
        </div>
      </DashboardLayout>

      <UsersDialogs onDataRefresh={refetch} />
    </UsersProvider>
  )
}
