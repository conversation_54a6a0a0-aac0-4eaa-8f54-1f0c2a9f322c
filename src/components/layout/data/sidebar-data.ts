import {
  IconBarrierBlock,
  IconBug,
  IconDeviceDesktop,
  IconError404,
  IconHelp,
  IconLayoutDashboard,
  IconLock,
  IconPalette,
  IconServerOff,
  IconSettings,
  IconTool,
  IconUserCog,
  IconUserOff,
  IconUsers,
  IconLogs,
  IconUsersGroup,
  IconDevices,
  IconCategory
} from '@tabler/icons-react'
import { type SidebarDataConfig } from '../types'

export const sidebarDataConfig: SidebarDataConfig = {
  user: {
    name: 'Beacon',
    email: '<EMAIL>',
    avatar: '/logo.png',
  },
  navGroups: [
    {
      titleKey: 'sidebar.nav.general',
      items: [
        {
          titleKey: 'sidebar.nav.dashboard',
          url: '/dashboard',
          icon: IconLayoutDashboard,
        },
      ],
    },
    {
      titleKey: 'sidebar.nav.mqtt',
      items: [
        {
          titleKey: 'sidebar.nav.groups',
          icon: IconUsersGroup,
          url: '/mqtt/groups',
        },
      ],
    },
    {
      titleKey: 'sidebar.nav.adminPages',
      requiresAdmin: true,
      items: [
        {
          titleKey: 'sidebar.nav.monitor',
          icon: IconDeviceDesktop,
          url: '/admin/monitor',
        },
        {
          titleKey: 'sidebar.nav.users',
          icon: IconUsers,
          url: '/admin/users',
        },
        {
          titleKey: 'sidebar.nav.devices',
          icon: IconDevices,
          items: [
            {
              titleKey: 'sidebar.nav.deviceManagement',
              url: '/admin/devices',
              icon: IconDevices,
            },
            {
              titleKey: 'sidebar.nav.deviceTypes',
              url: '/admin/device-types',
              icon: IconCategory,
            },
          ],
        },
        {
          titleKey: 'sidebar.nav.logs',
          icon: IconLogs,
          url: '/admin/logs',
        },
      ],
    },
    {
      titleKey: 'sidebar.nav.errorPages',
      items: [
        {
          titleKey: 'sidebar.nav.errorPages',
          icon: IconBug,
          items: [
            {
              titleKey: 'sidebar.nav.error401',
              url: '/401',
              icon: IconLock,
            },
            {
              titleKey: 'sidebar.nav.error403',
              url: '/403',
              icon: IconUserOff,
            },
            {
              titleKey: 'sidebar.nav.error404',
              url: '/404',
              icon: IconError404,
            },
            {
              titleKey: 'sidebar.nav.error500',
              url: '/500',
              icon: IconServerOff,
            },
            {
              titleKey: 'sidebar.nav.error503',
              url: '/503',
              icon: IconBarrierBlock,
            },
          ],
        },
      ],
    },
    {
      titleKey: 'sidebar.nav.settings',
      items: [
        {
          titleKey: 'sidebar.nav.settings',
          icon: IconSettings,
          items: [
            {
              titleKey: 'sidebar.nav.profile',
              url: '/settings',
              icon: IconUserCog,
            },
            {
              titleKey: 'sidebar.nav.account',
              url: '/settings/account',
              icon: IconTool,
            },
            {
              titleKey: 'sidebar.nav.appearance',
              url: '/settings/appearance',
              icon: IconPalette,
            },
          ],
        },
        {
          titleKey: 'sidebar.nav.help',
          url: '/help-center',
          icon: IconHelp,
        },
      ],
    },
  ],
}
